import { notFound } from "next/navigation"

import { getAdminProjectById } from "@/app/actions/admin"
import { getAllCategories } from "@/app/actions/projects"
import { AdminProjectEditForm } from "@/components/admin/admin-project-edit-form"

interface AdminProjectEditPageProps {
  params: Promise<{
    projectId: string
  }>
}

export default async function AdminProjectEditPage({ params }: AdminProjectEditPageProps) {
  const { projectId } = await params
  
  const [projectResult, categoriesData] = await Promise.all([
    getAdminProjectById(projectId),
    getAllCategories(),
  ])
  
  if (!projectResult.success || !projectResult.project) {
    notFound()
  }

  return (
    <AdminProjectEditForm 
      project={projectResult.project} 
      categories={categoriesData}
    />
  )
}
