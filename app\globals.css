@import "tailwindcss";
@import "@fuma-comment/react/preset.css";

@import "uploadthing/tw/v4";
@source "../node_modules/@uploadthing/react/dist"; /** <-- depends on your project structure */

/* Path du package relatif au fichier CSS */
@source '../node_modules/@fuma-comment/react/dist/**/*.js';

@plugin "tailwindcss-animate";
@plugin "@tailwindcss/typography";

/* Styles spécifiques pour Fuma Comment */
/* Fix pour les popups de menu qui apparaissent derrière le contenu */
div[role="menu"],
div[role="dialog"],
[data-radix-popper-content-wrapper] {
  z-index: 50 !important;
}

/* S'assurer que le composant principal a un z-index inférieur */
[data-fuma-comment] {
  z-index: 10;
  position: relative;
}
[data-fuma-comment-button] button,
.fuma-comment button,
.fuma-comment [role="button"] {
  cursor: pointer !important;
}

@custom-variant dark (&:is(.dark *));

:root {
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 3.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 3.9%);
  --primary: rgb(37, 99, 235);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(0 0% 96.1%);
  --secondary-foreground: hsl(0 0% 9%);
  --muted: hsl(0 0% 96.1%);
  --muted-foreground: hsl(0 0% 45.1%);
  --accent: hsl(217, 91%, 60%);
  --accent-foreground: hsl(0 0% 100%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 89.8%);
  --input: hsl(0 0% 89.8%);
  --ring: hsl(217, 91%, 60%);
  --chart-1: hsl(217, 91%, 60%);
  --chart-2: hsl(217, 70%, 50%);
  --chart-3: hsl(217, 50%, 40%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --radius: 0.6rem;
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 3.9%);
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(217, 91%, 60%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(217, 70%, 97%);
  --sidebar-accent-foreground: hsl(217, 91%, 60%);
  --sidebar-border: hsl(217, 10%, 91%);
  --sidebar-ring: hsl(217, 91%, 60%);
}

.dark {
  --background: hsl(0 0% 10%);
  --foreground: hsl(0 0% 98%);
  --card: hsl(0 0% 12%);
  --card-foreground: hsl(0 0% 98%);
  --popover: hsl(0 0% 10%);
  --popover-foreground: hsl(0 0% 98%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(0 0% 14.9%);
  --secondary-foreground: hsl(0 0% 98%);
  --muted: hsl(0 0% 14.9%);
  --muted-foreground: hsl(0 0% 63.9%);
  --accent: hsl(217, 70%, 45%);
  --accent-foreground: hsl(0 0% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 14.9%);
  --input: hsl(0 0% 14.9%);
  --ring: hsl(217, 91%, 60%);
  --chart-1: hsl(217, 91%, 60%);
  --chart-2: hsl(217, 70%, 45%);
  --chart-3: hsl(217, 50%, 40%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(0 0% 10%);
  --sidebar-foreground: hsl(0 0% 95.9%);
  --sidebar-primary: hsl(217, 91%, 60%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(0 0% 15.9%);
  --sidebar-accent-foreground: hsl(0 0% 95.9%);
  --sidebar-border: hsl(0 0% 15.9%);
  --sidebar-ring: hsl(217, 91%, 60%);
}

@theme inline {
  --font-sans: var(--font-sans);
  --font-heading: var(--font-heading);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Styles personnalisés pour Open Launch */
.yt-card {
  @apply bg-card border-border overflow-hidden rounded-lg border transition-all duration-200 hover:shadow-md;
}

.yt-card:hover {
  @apply -translate-y-1 transform;
}

.yt-button {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 rounded-md font-medium;
}

.yt-tag {
  @apply bg-secondary text-secondary-foreground inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
}

.yt-upvote {
  @apply hover:bg-secondary inline-flex items-center gap-1 rounded-md px-2 py-1 text-sm transition-colors;
}

.yt-upvote.active {
  @apply text-primary;
}
