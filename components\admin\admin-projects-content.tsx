import { getAdminProjects } from "@/app/actions/admin"
import { getAllCategories } from "@/app/actions/projects"
import { AdminProjectsTable } from "./admin-projects-table"
import { AdminProjectsFilters } from "./admin-projects-filters"

interface AdminProjectsContentProps {
  searchParams: {
    page?: string
    search?: string
    status?: string
    launchType?: string
    category?: string
    sortBy?: string
    sortOrder?: string
  }
}

export async function AdminProjectsContent({ searchParams }: AdminProjectsContentProps) {
  // Đảm bảo searchParams là một object hợp lệ
  const params = searchParams
  
  const page = parseInt(params.page || "1")
  const search = params.search || ""
  const status = params.status || ""
  const launchType = params.launchType || ""
  const category = params.category || ""
  const sortBy = params.sortBy || "createdAt"
  const sortOrder = (params.sortOrder as "asc" | "desc") || "desc"

  // Fetch projects and categories in parallel
  const [projectsData, categoriesData] = await Promise.all([
    getAdminProjects({
      page,
      limit: 10,
      search,
      status,
      launchType,
      category,
      sortBy,
      sortOrder,
    }),
    getAllCategories(),
  ])

  return (
    <div className="space-y-6">
      <AdminProjectsFilters
        categories={categoriesData}
        currentFilters={{
          search,
          status,
          launchType,
          category,
          sortBy,
          sortOrder,
        }}
      />
      
      <AdminProjectsTable
        projects={projectsData.projects}
        totalCount={projectsData.totalCount}
        totalPages={projectsData.totalPages}
        currentPage={projectsData.currentPage}
      />
    </div>
  )
}
