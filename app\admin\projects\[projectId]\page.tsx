import { notFound } from "next/navigation"

import { getAdminProjectById } from "@/app/actions/admin"
import { AdminProjectDetails } from "@/components/admin/admin-project-details"

interface AdminProjectDetailPageProps {
  params: Promise<{
    projectId: string
  }>
}

export default async function AdminProjectDetailPage({ params }: AdminProjectDetailPageProps) {
  const { projectId } = await params
  
  const result = await getAdminProjectById(projectId)
  
  if (!result.success || !result.project) {
    notFound()
  }

  return <AdminProjectDetails project={result.project} />
}
