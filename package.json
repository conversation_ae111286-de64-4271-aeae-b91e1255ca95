{"name": "open-launch", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --turbopack", "lint": "next lint", "prepare": "npx simple-git-hooks", "start": "next start", "update-launches": "bash ./scripts/update-launches.sh"}, "simple-git-hooks": {"pre-commit": "npx lint-staged --verbose", "commit-msg": "npx commitlint --edit $1"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*": "prettier --write --ignore-unknown", "package.json": "sort-package-json"}, "prettier": {"importOrder": ["<BUILTIN_MODULES>", "", "^react/(.*)$|^react$", "^next/(.*)$|^next$", "", "<THIRD_PARTY_MODULES>", "", "^@/types/(.*)$", "^@/config/(.*)$", "^@/lib/(.*)$", "^@/hooks/(.*)$", "^@/db/(.*)$", "^@/components/ui/(.*)$", "^@/components/(.*)$", "^@/app/(.*)$", "", "^[./]"], "plugins": ["@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "printWidth": 100, "semi": false}, "dependencies": {"@better-auth/stripe": "^1.2.7", "@fuma-comment/next": "^1.2.3", "@fuma-comment/react": "^1.2.3", "@fuma-comment/server": "^1.2.3", "@hookform/resolvers": "^4.1.3", "@marsidev/react-turnstile": "^1.1.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.3", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@remixicon/react": "^4.6.0", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/mdx": "^2.0.13", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@uploadthing/react": "^7.3.1", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.40.1", "embla-carousel-react": "^8.6.0", "emblor": "^1.4.8", "gray-matter": "^4.0.3", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "lucide-react": "^0.475.0", "motion": "^12.10.5", "next": "^15.3.2", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "pg": "^8.15.6", "postcss": "^8.5.3", "react": "^18.2.0", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.9", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx-frontmatter": "^5.2.0", "remixicon": "^4.6.0", "remixicon-react": "^1.0.0", "resend": "^4.5.1", "satori": "^0.12.2", "sonner": "^2.0.3", "stripe": "^17.7.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.2", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.3.1", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@types/node": "^20.17.46", "@types/pg": "^8.15.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "drizzle-kit": "^0.30.6", "eslint": "^9.26.0", "eslint-config-next": "15.1.7", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "simple-git-hooks": "^2.13.0", "sort-package-json": "^3.2.1", "tsx": "^4.19.4", "typescript": "^5.8.3"}}